import { notFound } from "next/navigation";
import { verifyEmailToken } from "@/lib/email-verification";
import { CheckCircle, XCircle, Mail, AlertTriangle } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "Verificare Email - Portavio",
  description: "Verificați adresa de email pentru a activa contul Portavio",
};

interface VerifyEmailPageProps {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

export default async function VerifyEmailPage({
  searchParams,
}: VerifyEmailPageProps) {
  const params = await searchParams;
  const token = params.token;

  if (!token || typeof token !== "string") {
    notFound();
  }

  const result = await verifyEmailToken(token);

  return (
    <div className="container mx-auto py-8 px-4 max-w-2xl">
      <div className="mb-8 text-center">
        <h1 className="text-3xl font-bold mb-2 flex items-center justify-center gap-2">
          <Mail className="h-8 w-8 text-portavio-orange" />
          Verificare Email
        </h1>
        <p className="text-muted-foreground">
          Verificarea adresei de email pentru contul Portavio
        </p>
      </div>

      <Card>
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            {result.success ? (
              <CheckCircle className="h-16 w-16 text-green-500" />
            ) : (
              <XCircle className="h-16 w-16 text-red-500" />
            )}
          </div>
          <CardTitle
            className={
              result.success
                ? "dark:text-green-400 text-green-700"
                : "dark:text-red-400 text-red-700"
            }
          >
            {result.success ? "Verificare Reușită!" : "Verificare Eșuată"}
          </CardTitle>
          <CardDescription className="text-base">
            {result.message}
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-4">
          {result.success && (
            <div className="space-y-4">
              {(result.error === "WELCOME_EMAIL_FAILED" ||
                result.error === "TOKEN_DECODE_FAILED" ||
                result.error === "NO_EMAIL_INFO") && (
                <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md">
                  <div className="flex items-start gap-2">
                    <AlertTriangle className="h-4 w-4 text-yellow-600 dark:text-yellow-400 mt-0.5" />
                    <div className="text-sm text-yellow-800 dark:text-yellow-200">
                      <p className="font-medium">Atenție</p>
                      <p className="text-xs mt-1">
                        {result.error === "WELCOME_EMAIL_FAILED" &&
                          "Contul a fost verificat cu succes, dar nu am putut trimite email-ul de bun venit."}
                        {result.error === "TOKEN_DECODE_FAILED" &&
                          "Contul a fost verificat cu succes, dar nu am putut trimite email-ul de bun venit din cauza unei probleme tehnice."}
                        {result.error === "NO_EMAIL_INFO" &&
                          "Contul a fost verificat cu succes, dar nu am putut trimite email-ul de bun venit."}
                      </p>
                    </div>
                  </div>
                </div>
              )}

              <div className="flex flex-col sm:flex-row gap-3 pt-4">
                <Button asChild className="flex-1">
                  <Link href="/profile">Conectează-te</Link>
                </Button>
              </div>
            </div>
          )}

          {!result.success && (
            <div className="space-y-4">
              <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
                <div className="text-sm text-red-800 dark:text-red-200">
                  <p className="font-medium mb-2">Ce poți face:</p>
                  <ul className="list-disc list-inside space-y-1 text-xs">
                    <li>Verifică dacă link-ul din email este complet</li>
                    <li>Încearcă să accesezi din nou link-ul din email</li>
                    <li>Contactează suportul tehnic dacă problema persistă</li>
                  </ul>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-3 pt-4">
                <Button variant="outline" asChild className="flex-1">
                  <Link href="/auth/signin">Încearcă să te Conectezi</Link>
                </Button>
                <Button variant="outline" asChild className="flex-1">
                  <Link href="/contact">Contactează Suportul</Link>
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
