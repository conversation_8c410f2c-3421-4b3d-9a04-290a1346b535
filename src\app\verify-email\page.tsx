import { notFound } from "next/navigation";
import { auth } from "@/lib/auth";
import { sendEmail } from "@/lib/sendEmail";
import { APIError } from "better-auth/api";
import { CheckCircle, XCircle, Mail, AlertTriangle } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "Verificare Email - Portavio",
  description: "Verificați adresa de email pentru a activa contul Portavio",
};

interface VerifyEmailPageProps {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

interface VerificationResult {
  success: boolean;
  message: string;
  userEmail?: string;
  userName?: string;
  welcomeEmailSent?: boolean;
  error?: string;
}

async function verifyEmailToken(token: string): Promise<VerificationResult> {
  try {
    // Verify email using better-auth API
    const result: any = await auth.api.verifyEmail({
      query: { token },
    });

    console.log("result after verifyEmail first method call: ", result);

    if (!result || !result.user) {
      return {
        success: false,
        message: "Token-ul de verificare este invalid sau a expirat.",
        error: "INVALID_TOKEN",
      };
    }

    const user = result.user;

    // Send welcome email after successful verification
    try {
      await sendWelcomeEmail(user.email, user.name || user.username || "");

      console.log("Email trimis cu succes?", user);

      return {
        success: true,
        message: "Email-ul a fost verificat cu succes! Bun venit la Portavio!",
        userEmail: user.email,
        userName: user.name || user.username || "",
        welcomeEmailSent: true,
      };
    } catch (emailError) {
      console.error("Failed to send welcome email:", emailError);

      // Verification succeeded but welcome email failed
      return {
        success: true,
        message: "Email-ul a fost verificat cu succes! Bun venit la Portavio!",
        userEmail: user.email,
        userName: user.name || user.username || "",
        welcomeEmailSent: false,
        error: "WELCOME_EMAIL_FAILED",
      };
    }
  } catch (error) {
    console.error("Email verification error:", error);

    if (error instanceof APIError) {
      // Handle specific better-auth errors
      switch (error.status) {
        case 400:
          return {
            success: false,
            message: "Token-ul de verificare este invalid.",
            error: "INVALID_TOKEN",
          };
        case 404:
          return {
            success: false,
            message: "Token-ul de verificare nu a fost găsit sau a expirat.",
            error: "TOKEN_NOT_FOUND",
          };
        case 410:
          return {
            success: false,
            message:
              "Token-ul de verificare a expirat. Vă rugăm să solicitați un nou email de verificare.",
            error: "TOKEN_EXPIRED",
          };
        default:
          return {
            success: false,
            message:
              "S-a produs o eroare la verificarea email-ului. Vă rugăm să încercați din nou.",
            error: "VERIFICATION_ERROR",
          };
      }
    }

    return {
      success: false,
      message:
        "S-a produs o eroare neașteptată. Vă rugăm să contactați suportul tehnic.",
      error: "UNEXPECTED_ERROR",
    };
  }
}

async function sendWelcomeEmail(
  userEmail: string,
  userName: string
): Promise<void> {
  const welcomeSubject = "Bun venit la Portavio!";
  const welcomeHtml = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #ff7f50; margin-bottom: 10px;">Bun venit la Portavio!</h1>
        <p style="color: #666; font-size: 16px;">Contul tău a fost activat cu succes</p>
      </div>
      
      <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
        <h2 style="color: #333; margin-bottom: 15px;">Salut${
          userName ? `, ${userName}` : ""
        }!</h2>
        <p style="color: #555; line-height: 1.6; margin-bottom: 15px;">
          Îți mulțumim că te-ai alăturat platformei Portavio! Email-ul tău a fost verificat cu succes 
          și contul tău este acum activ.
        </p>
        <p style="color: #555; line-height: 1.6;">
          Acum poți începe să îți urmărești portofoliul în detaliu și să beneficiezi de toate 
          funcționalitățile platformei noastre.
        </p>
      </div>
      
      <div style="text-align: center; margin: 30px 0;">
        <a href="${
          process.env.BETTER_AUTH_URL || "http://localhost:3000"
        }/profile" 
           style="background-color: #ff7f50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">
          Accesează Profilul
        </a>
      </div>
      
      <div style="border-top: 1px solid #eee; padding-top: 20px; margin-top: 30px;">
        <p style="color: #888; font-size: 12px; text-align: center;">
          Dacă ai întrebări, nu ezita să ne <NAME_EMAIL>
        </p>
        <p style="color: #888; font-size: 12px; text-align: center;">
          © ${new Date().getFullYear()} Portavio. Toate drepturile rezervate.
        </p>
      </div>
    </div>
  `;

  const welcomeText = `
Bun venit la Portavio!

Salut${userName ? `, ${userName}` : ""}!

Îți mulțumim că te-ai alăturat platformei Portavio! Email-ul tău a fost verificat cu succes și contul tău este acum activ.

Acum poți începe să îți urmărești portofoliul în detaliu și să beneficiezi de toate funcționalitățile platformei noastre.

Accesează profilul tău: ${
    process.env.BETTER_AUTH_URL || "http://localhost:3000"
  }/profile

Dacă ai întrebări, nu ezita să ne <NAME_EMAIL>

© ${new Date().getFullYear()} Portavio. Toate drepturile rezervate.
  `;

  await sendEmail({
    to: userEmail,
    subject: welcomeSubject,
    html: welcomeHtml,
    text: welcomeText,
  });
}

export default async function VerifyEmailPage({
  searchParams,
}: VerifyEmailPageProps) {
  const params = await searchParams;
  const token = params.token;

  // Check if token is provided
  if (!token || typeof token !== "string") {
    notFound();
  }

  // Verify the email token
  const result = await verifyEmailToken(token);

  console.log("result after verifyEmailToken: ", result);

  return (
    <div className="container mx-auto py-8 px-4 max-w-2xl">
      <div className="mb-8 text-center">
        <h1 className="text-3xl font-bold mb-2 flex items-center justify-center gap-2">
          <Mail className="h-8 w-8 text-portavio-orange" />
          Verificare Email
        </h1>
        <p className="text-muted-foreground">
          Verificarea adresei de email pentru contul Portavio
        </p>
      </div>

      <Card>
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            {result.success ? (
              <CheckCircle className="h-16 w-16 text-green-500" />
            ) : (
              <XCircle className="h-16 w-16 text-red-500" />
            )}
          </div>
          <CardTitle
            className={
              result.success
                ? "text-green-700"
                : "dark:text-red-400 text-red-700"
            }
          >
            {result.success ? "Verificare Reușită!" : "Verificare Eșuată"}
          </CardTitle>
          <CardDescription className="text-base">
            {result.message}
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-4">
          {result.success && (
            <div className="space-y-4">
              {result.welcomeEmailSent && (
                <div className="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md">
                  <div className="flex items-start gap-2">
                    <Mail className="h-4 w-4 text-blue-600 dark:text-blue-400 mt-0.5" />
                    <div className="text-sm text-blue-800 dark:text-blue-200">
                      <p className="font-medium">Email de bun venit trimis!</p>
                      <p className="text-xs mt-1">
                        Am trimis un email de bun venit la adresa{" "}
                        {result.userEmail}
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {result.error === "WELCOME_EMAIL_FAILED" && (
                <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md">
                  <div className="flex items-start gap-2">
                    <AlertTriangle className="h-4 w-4 text-yellow-600 dark:text-yellow-400 mt-0.5" />
                    <div className="text-sm text-yellow-800 dark:text-yellow-200">
                      <p className="font-medium">Atenție</p>
                      <p className="text-xs mt-1">
                        Contul a fost verificat cu succes, dar nu am putut
                        trimite email-ul de bun venit.
                      </p>
                    </div>
                  </div>
                </div>
              )}

              <div className="flex flex-col sm:flex-row gap-3 pt-4">
                <Button asChild className="flex-1">
                  <Link href="/profile">Accesează Profilul</Link>
                </Button>
                <Button variant="outline" asChild className="flex-1">
                  <Link href="/">Înapoi la Pagina Principală</Link>
                </Button>
              </div>
            </div>
          )}

          {!result.success && (
            <div className="space-y-4">
              <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
                <div className="text-sm text-red-800 dark:text-red-200">
                  <p className="font-medium mb-2">Ce poți face:</p>
                  <ul className="list-disc list-inside space-y-1 text-xs">
                    <li>Verifică dacă link-ul din email este complet</li>
                    <li>Încearcă să accesezi din nou link-ul din email</li>
                    <li>Contactează suportul tehnic dacă problema persistă</li>
                  </ul>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-3 pt-4">
                <Button variant="outline" asChild className="flex-1">
                  <Link href="/auth/signin">Încearcă să te Conectezi</Link>
                </Button>
                <Button variant="outline" asChild className="flex-1">
                  <Link href="/contact">Contactează Suportul</Link>
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
