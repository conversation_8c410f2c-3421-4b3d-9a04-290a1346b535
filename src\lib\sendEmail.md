# Email Service Documentation

## Overview

The `sendEmail.ts` library provides a robust email sending service using Nodemailer with SMTP configuration stored in the database. It automatically fetches mail configuration from the `ptvuser_mail_config` table and sends emails with proper error handling.

## Features

- ✅ **Database-driven configuration**: SMTP settings stored in PostgreSQL
- ✅ **Automatic fallback**: Uses default config or first available config
- ✅ **Error handling**: Comprehensive error messages in Romanian
- ✅ **Type safety**: Full TypeScript support
- ✅ **Connection verification**: Validates SMTP connection before sending
- ✅ **Test functionality**: Built-in test email function

## Database Schema

The service expects a `ptvuser_mail_config` table with the following structure:

```sql
CREATE TABLE ptvuser_mail_config (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  smtp_host VARCHAR(255) NOT NULL,
  smtp_port INTEGER NOT NULL DEFAULT 587,
  smtp_user VARCHAR(255) NOT NULL,
  smtp_pass VARCHAR(255) NOT NULL,
  smtp_secure BOOLEAN DEFAULT FALSE,
  is_default BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Usage Examples

### Basic Email Sending

```typescript
import { sendEmail } from "@/lib/sendEmail";

// Send a simple text email
await sendEmail({
  to: "<EMAIL>",
  subject: "Welcome to Portavio",
  text: "Welcome to our platform!",
});

// Send an HTML email
await sendEmail({
  to: "<EMAIL>",
  subject: "Welcome to Portavio",
  html: `
    <h1>Welcome to Portavio!</h1>
    <p>Thank you for joining our platform.</p>
  `,
});

// Send email with custom from address
await sendEmail({
  to: "<EMAIL>",
  subject: "Custom Sender",
  text: "This email has a custom sender.",
  from: '"Custom Name" <<EMAIL>>',
});
```

### Test Email Function

```typescript
import { sendTestEmail } from "@/lib/sendEmail";

// Send a test email to verify configuration
await sendTestEmail("<EMAIL>");
```

### In API Routes

```typescript
// app/api/send-notification/route.ts
import { NextRequest, NextResponse } from "next/server";
import { sendEmail } from "@/lib/sendEmail";

export async function POST(request: NextRequest) {
  try {
    const { userEmail, message } = await request.json();

    await sendEmail({
      to: userEmail,
      subject: "Notification from Portavio",
      html: `
        <div style="font-family: Arial, sans-serif;">
          <h2>Notification</h2>
          <p>${message}</p>
        </div>
      `,
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Email sending failed" },
      { status: 500 }
    );
  }
}
```

### In Server Actions

```typescript
// app/actions/send-welcome-email.ts
"use server";

import { sendEmail } from "@/lib/sendEmail";

export async function sendWelcomeEmail(userEmail: string, userName: string) {
  try {
    await sendEmail({
      to: userEmail,
      subject: "Bun venit la Portavio!",
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px;">
          <h1 style="color: #ff7f50;">Bun venit, ${userName}!</h1>
          <p>Îți mulțumim că te-ai alăturat platformei Portavio.</p>
          <p>Acum poți începe să îți urmărești portofoliul în detaliu.</p>
        </div>
      `,
    });
    return { success: true };
  } catch (error) {
    console.error("Failed to send welcome email:", error);
    return { success: false, error: error instanceof Error ? error.message : "Unknown error" };
  }
}
```

## Error Handling

The service provides detailed error messages in Romanian:

```typescript
try {
  await sendEmail({
    to: "<EMAIL>",
    subject: "Test",
    text: "Test message",
  });
} catch (error) {
  // Possible error messages:
  // - "Nu s-a găsit nicio configurație de email în baza de date"
  // - "Configurația de email este incompletă"
  // - "Eroare la trimiterea email-ului: [specific error]"
  console.error("Email error:", error.message);
}
```

## Configuration Priority

1. **Default configuration**: Looks for `is_default = true`
2. **First available**: If no default found, uses the first configuration
3. **Error**: If no configuration exists, throws an error

## Security Considerations

- SMTP passwords are stored in the database (consider encryption)
- TLS is configured to allow self-signed certificates
- Connection verification prevents sending with invalid configs
- All errors are logged for debugging

## Testing

Use the test page at `/test-email` to verify your SMTP configuration:

1. Navigate to `http://localhost:3001/test-email`
2. Enter a test email address
3. Click "Trimite Email de Test"
4. Check the recipient's inbox (and spam folder)

## Environment Variables Required

Make sure these environment variables are set:

```env
HASURA_ENDPOINT=your_hasura_endpoint
HASURA_SECRET=your_hasura_admin_secret
```

## Common SMTP Settings

### Gmail
```sql
INSERT INTO ptvuser_mail_config (name, smtp_host, smtp_port, smtp_user, smtp_pass, smtp_secure, is_default)
VALUES ('Gmail', 'smtp.gmail.com', 587, '<EMAIL>', 'your-app-password', false, true);
```

### Outlook/Hotmail
```sql
INSERT INTO ptvuser_mail_config (name, smtp_host, smtp_port, smtp_user, smtp_pass, smtp_secure, is_default)
VALUES ('Outlook', 'smtp-mail.outlook.com', 587, '<EMAIL>', 'your-password', false, true);
```

### Custom SMTP
```sql
INSERT INTO ptvuser_mail_config (name, smtp_host, smtp_port, smtp_user, smtp_pass, smtp_secure, is_default)
VALUES ('Custom SMTP', 'mail.yourdomain.com', 587, '<EMAIL>', 'your-password', false, true);
```
