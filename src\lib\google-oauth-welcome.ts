import { hasuraQuery } from "@/utils/db";
import { sendWelcomeEmail } from "./email-verification";

interface UserCreationInfo {
  id: string;
  email: string;
  name?: string;
  createdAt: string;
}

/**
 * Check if a user was created recently (within the last 5 minutes)
 * This helps determine if this is a new user from Google OAuth
 */
async function isRecentlyCreatedUser(userEmail: string): Promise<boolean> {
  try {
    const fiveMinutesAgo = new Date(Date.now() - 2 * 60 * 1000).toISOString();

    const query = `
      query CheckRecentUser($email: String!, $since: timestamp!) {
        ptvuser_user(
          where: {
            email: {_eq: $email},
            createdAt: {_gte: $since}
          },
          limit: 1
        ) {
          id
          email
          name
          createdAt
        }
      }
    `;

    const result = await hasuraQuery<{
      ptvuser_user: UserCreationInfo[];
    }>(query, {
      variables: {
        email: userEmail,
        since: fiveMinutesAgo,
      },
    });

    return result.ptvuser_user?.length > 0;
  } catch (error) {
    console.error("Error checking if user was recently created:", error);
    return false;
  }
}

/**
 * Check if a user has Google OAuth account
 */
async function hasGoogleAccount(userId: string): Promise<boolean> {
  try {
    const query = `
      query CheckGoogleAccount($userId: String!) {
        ptvuser_account(
          where: {
            userId: {_eq: $userId}, 
            providerId: {_eq: "google"}
          }, 
          limit: 1
        ) {
          id
          providerId
        }
      }
    `;

    const result = await hasuraQuery<{
      ptvuser_account: Array<{
        id: string;
        providerId: string;
      }>;
    }>(query, { variables: { userId } });

    return result.ptvuser_account?.length > 0;
  } catch (error) {
    console.error("Error checking Google account:", error);
    return false;
  }
}

/**
 * Handle Google OAuth welcome email logic
 * This detects new Google OAuth users regardless of signin/signup entry point
 */
export async function handleGoogleOAuthWelcome(
  userEmail: string,
  userName?: string,
  userId?: string
): Promise<{ success: boolean; message: string; emailSent: boolean }> {
  try {
    // First, verify this user has a Google account (required for Google OAuth welcome)
    if (!userId) {
      return {
        success: true,
        message: "No user ID provided, skipping welcome email check",
        emailSent: false,
      };
    }

    const hasGoogle = await hasGoogleAccount(userId);
    if (!hasGoogle) {
      return {
        success: true,
        message: "User doesn't have Google account, no welcome email needed",
        emailSent: false,
      };
    }

    // Check if this is a recently created user (new signup)
    const isNewUser = await isRecentlyCreatedUser(userEmail);

    if (!isNewUser) {
      return {
        success: true,
        message: "Existing user, no welcome email needed",
        emailSent: false,
      };
    }

    // This is a new Google OAuth user - send welcome email
    await sendWelcomeEmail(userEmail, userName);

    console.log(`Welcome email sent to new Google OAuth user: ${userEmail}`);

    return {
      success: true,
      message: "Welcome email sent successfully",
      emailSent: true,
    };
  } catch (error) {
    console.error("Error handling Google OAuth welcome:", error);
    return {
      success: false,
      message: "Failed to send welcome email",
      emailSent: false,
    };
  }
}
