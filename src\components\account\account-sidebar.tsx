"use client";

import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { <PERSON>, Lock, Settings, User, UserCheck } from "lucide-react";

export type AccountSection =
  | "detalii-cont"
  | "schimbare-parola"
  | "conturi-conectate"
  | "notificari"
  | "setari";

interface AccountSidebarProps {
  activeSection: AccountSection;
  onSectionChange: (section: AccountSection) => void;
}

const menuItems: Array<{
  id: AccountSection;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
  available: boolean;
}> = [
  {
    id: "detalii-cont",
    label: "Detalii cont",
    icon: User,
    description: "Informații personale și profil",
    available: true,
  },
  {
    id: "schimbare-parola",
    label: "Schimbare parolă",
    icon: Lock,
    description: "Actualizează parola contului",
    available: false,
  },
  {
    id: "conturi-conectate",
    label: "Conturi conectate",
    icon: User<PERSON><PERSON><PERSON>,
    description: "Gestionează conturile conectate",
    available: false,
  },
  {
    id: "notificari",
    label: "Notificări",
    icon: Bell,
    description: "Preferințe de notificare",
    available: false,
  },
  {
    id: "setari",
    label: "Setări",
    icon: Settings,
    description: "Preferințe aplicație",
    available: false,
  },
];

export function AccountSidebar({
  activeSection,
  onSectionChange,
}: AccountSidebarProps) {
  return (
    <div className="w-full lg:w-80 bg-card border border-border lg:rounded-bl-lg lg:rounded-tl-lg lg:rounded-tr-none rounded-tl-lg rounded-tr-lg p-6 shadow-lg">
      <div className="space-y-1">
        <h2 className="text-lg font-semibold mb-4">Contul meu</h2>

        <nav className="space-y-1">
          {menuItems.map((item) => {
            const Icon = item.icon;
            const isActive = activeSection === item.id;
            const isAvailable = item.available;

            return (
              <Button
                key={item.id}
                variant={isActive ? "default" : "ghost"}
                className={cn(
                  "w-full justify-start h-auto p-3 text-left overflow-hidden",
                  isActive &&
                    "bg-portavio-orange hover:bg-portavio-orange-hover",
                  !isAvailable && "opacity-50 cursor-not-allowed"
                )}
                onClick={() => isAvailable && onSectionChange(item.id)}
                disabled={!isAvailable}
              >
                <div className="flex items-start gap-3 w-full">
                  <Icon
                    className={cn(
                      "h-5 w-5 mt-0.5 flex-shrink-0",
                      isActive ? "text-white" : "text-muted-foreground"
                    )}
                  />
                  <div className="flex-1 min-w-0">
                    <div
                      className={cn(
                        "font-medium text-sm w-fit",
                        isActive ? "text-white" : "text-foreground"
                      )}
                    >
                      {item.label}
                      {!isAvailable && (
                        <span className="ml-2 text-xs opacity-60">
                          (În curând)
                        </span>
                      )}
                    </div>
                    <div
                      className={cn(
                        "text-xs mt-0.5",
                        isActive ? "text-white/80" : "text-muted-foreground"
                      )}
                    >
                      {item.description}
                    </div>
                  </div>
                </div>
              </Button>
            );
          })}
        </nav>
      </div>
    </div>
  );
}
