"use client";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { getImageSourceType } from "@/lib/account-schemas";
import { Camera, Upload, X } from "lucide-react";
import Image from "next/image";
import { useRef, useState } from "react";

interface ProfilePictureUploadProps {
  currentImage?: string;
  username?: string;
  name?: string;
  onImageUpload: (file: File) => Promise<void>;
  onImageRemove?: () => Promise<void>;
  isLoading?: boolean;
  onImageChange?: () => void;
}

export function ProfilePictureUpload({
  currentImage,
  username,
  name,
  onImageUpload,
  onImageRemove,
  isLoading = false,
  onImageChange,
}: ProfilePictureUploadProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [dragOver, setDragOver] = useState(false);

  const handleFileSelect = async (file: File) => {
    if (file && file.type.startsWith("image/")) {
      try {
        await onImageUpload(file);
        if (onImageChange) {
          onImageChange();
        }
      } catch (error) {
        console.error("Error uploading image:", error);
      }
    }
  };

  const handleFileInputChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setDragOver(false);

    const file = event.dataTransfer.files[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = () => {
    setDragOver(false);
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  const handleImageRemove = async () => {
    if (onImageRemove) {
      try {
        await onImageRemove();
        if (onImageChange) {
          onImageChange();
        }

        if (fileInputRef.current) {
          fileInputRef.current.value = "";
        }
      } catch (error) {
        console.error("Error removing image:", error);
      }
    }
  };

  const imageSourceType = getImageSourceType(currentImage);
  const hasCustomImage = imageSourceType === "base64";
  const hasExternalImage = imageSourceType === "url";

  return (
    <div className="space-y-4">
      <Label className="text-sm font-medium">Imagine de profil</Label>

      <div className="flex flex-col items-center space-y-4">
        <div className="relative">
          {currentImage ? (
            <div className="relative">
              <div className="w-[140px] h-[140px] rounded-full overflow-hidden border-4 border-border relative">
                <Image
                  src={currentImage}
                  alt="Imagine de profil"
                  fill
                  className="object-cover"
                />
              </div>
              {hasCustomImage && onImageRemove && (
                <Button
                  type="button"
                  variant="destructive"
                  size="sm"
                  className="absolute -top-1 right-2 rounded-full h-8 w-8 p-0"
                  onClick={handleImageRemove}
                  disabled={isLoading}
                  title="Șterge imaginea"
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
          ) : (
            <div className="flex h-[140px] w-[140px] items-center justify-center rounded-full bg-portavio-orange text-white text-2xl font-semibold border-4 border-border">
              {username?.[0]?.toUpperCase() || name?.[0]?.toUpperCase() || "U"}
            </div>
          )}
        </div>

        <div
          className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
            dragOver
              ? "border-portavio-orange bg-portavio-orange/5"
              : "border-border hover:border-portavio-orange/50"
          }`}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
        >
          <div className="space-y-2" onClick={openFileDialog}>
            <Camera className="mx-auto h-8 w-8 text-muted-foreground" />
            <div className="space-y-1">
              <p className="text-sm font-medium">
                {hasExternalImage
                  ? "Înlocuiește imaginea de profil"
                  : "Încarcă o imagine de profil"}
              </p>
              <p className="text-xs text-muted-foreground">
                Trage și lasă aici sau fă clic pe butonul de mai jos pentru a
                selecta
              </p>
              <p className="text-xs text-muted-foreground">
                PNG, JPG, WebP până la 3MB
              </p>
            </div>
          </div>
        </div>

        <Button
          type="button"
          variant="outline"
          onClick={openFileDialog}
          disabled={isLoading}
          className="w-full max-w-xs"
        >
          <Upload className="mr-2 h-4 w-4" />
          {isLoading ? "Se încarcă..." : "Selectează fișier"}
        </Button>

        <Input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleFileInputChange}
          className="hidden"
        />

        {hasExternalImage && (
          <p className="text-xs text-muted-foreground text-center max-w-xs">
            Imaginea curentă provine de la{" "}
            {currentImage?.includes("google") ? "Google" : "furnizorul extern"}.
            Poți încărca o imagine personalizată care o va înlocui.
          </p>
        )}
      </div>
    </div>
  );
}
