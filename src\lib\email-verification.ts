import { auth } from "./auth";
import { sendEmail } from "./sendEmail";
import { APIError } from "better-auth/api";
import { hasuraQuery } from "@/utils/db/hasura";

export interface VerificationResult {
  success: boolean;
  message: string;
  userEmail?: string;
  userName?: string;
  welcomeEmailSent?: boolean;
  alreadyVerified?: boolean; // Flag to indicate if email was already verified
  isNewVerification?: boolean; // Flag to indicate if this was a new verification
  error?: string;
}

/**
 * Check if a user's email is already verified
 * @param userEmail Email address to check
 * @returns boolean indicating if email is verified
 */
async function checkEmailVerificationStatus(
  userEmail: string
): Promise<boolean> {
  try {
    const query = `
      query CheckEmailVerification($email: String!) {
        ptvuser_user(where: {email: {_eq: $email}}, limit: 1) {
          id
          email
          emailVerified
        }
      }
    `;

    const result = await hasuraQuery<{
      ptvuser_user: Array<{
        id: string;
        email: string;
        emailVerified: boolean;
      }>;
    }>(query, { variables: { email: userEmail } });

    const user = result.ptvuser_user?.[0];
    return user?.emailVerified || false;
  } catch (error) {
    console.error("Error checking email verification status:", error);
    // If we can't check the status, assume it's not verified to be safe
    return false;
  }
}

/**
 * Send verification email with professional Romanian template
 * @param userEmail Email address to send verification to
 * @param userName User's display name (optional)
 * @param verificationToken JWT token for email verification
 */
export async function sendVerificationEmail(
  userEmail: string,
  userName: string,
  verificationToken: string
): Promise<void> {
  const baseURL = process.env.BETTER_AUTH_URL || "http://localhost:3000";
  const verificationUrl = `${baseURL}/verify-email?token=${verificationToken}`;

  const emailSubject = "Verifică adresa de email - Portavio";
  const emailHtml = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #ff7f50; margin-bottom: 10px;">Verifică adresa de email</h1>
        <p style="color: #666; font-size: 16px;">Pentru a activa contul Portavio</p>
      </div>
      
      <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
        <h2 style="color: #333; margin-bottom: 15px;">Salut${
          userName ? `, ${userName}` : ""
        }!</h2>
        <p style="color: #555; line-height: 1.6; margin-bottom: 15px;">
          Îți mulțumim că te-ai înregistrat pe Portavio! Pentru a-ți activa contul,
          te rugăm să verifici adresa de email făcând clic pe butonul de mai jos.
        </p>
        <p style="color: #555; line-height: 1.6;">
          Acest link va expira în 24 de ore din motive de securitate.
        </p>
      </div>
      
      <div style="text-align: center; margin: 30px 0;">
        <a href="${verificationUrl}"
           style="background-color: #ff7f50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">
          Verifică Email-ul
        </a>
      </div>
      
      <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 6px; margin: 20px 0;">
        <p style="color: #856404; font-size: 14px; margin: 0;">
          <strong>Dacă nu te-ai înregistrat pe Portavio,</strong> poți ignora acest email în siguranță.
        </p>
      </div>
      
      <div style="border-top: 1px solid #eee; padding-top: 20px; margin-top: 30px;">
        <p style="color: #888; font-size: 12px; text-align: center;">
          Dacă butonul nu funcționează, copiază și lipește acest link în browser:<br>
          <a href="${verificationUrl}" style="color: #ff7f50; word-break: break-all;">${verificationUrl}</a>
        </p>
        <p style="color: #888; font-size: 12px; text-align: center; margin-top: 15px;">
          © ${new Date().getFullYear()} Portavio. Toate drepturile rezervate.
        </p>
      </div>
    </div>
  `;

  const emailText = `
    Verifică adresa de email - Portavio

    Salut${userName ? `, ${userName}` : ""}!

    Îți mulțumim că te-ai înregistrat pe Portavio! Pentru a-ți activa contul, te rugăm să verifici adresa de email accesând link-ul de mai jos:

    ${verificationUrl}

    Acest link va expira în 24 de ore din motive de securitate.

    Dacă nu te-ai înregistrat pe Portavio, poți ignora acest email în siguranță.

    © ${new Date().getFullYear()} Portavio. Toate drepturile rezervate.
  `;

  await sendEmail({
    to: userEmail,
    subject: emailSubject,
    html: emailHtml,
    text: emailText,
  });
}

/**
 * Send welcome email after successful verification
 * @param userEmail Email address to send welcome email to
 * @param userName User's display name (optional)
 */
export async function sendWelcomeEmail(
  userEmail: string,
  userName?: string
): Promise<void> {
  const welcomeSubject = "Bun venit la Portavio!";
  const welcomeHtml = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #ff7f50; margin-bottom: 10px;">Bun venit la Portavio!</h1>
        <p style="color: #666; font-size: 16px;">Contul tău a fost activat cu succes</p>
      </div>
      
      <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
        <h2 style="color: #333; margin-bottom: 15px;">Salut${
          userName ? `, ${userName}` : ""
        }!</h2>
        <p style="color: #555; line-height: 1.6; margin-bottom: 15px;">
          Îți mulțumim că te-ai alăturat platformei Portavio! Email-ul tău a fost verificat cu succes 
          și contul tău este acum activ.
        </p>
        <p style="color: #555; line-height: 1.6;">
          Acum poți începe să îți urmărești portofoliul în detaliu și să beneficiezi de toate 
          funcționalitățile platformei noastre.
        </p>
      </div>
      
      <div style="text-align: center; margin: 30px 0;">
        <a href="${
          process.env.BETTER_AUTH_URL || "http://localhost:3000"
        }/profile" 
           style="background-color: #ff7f50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">
          Accesează Profilul
        </a>
      </div>
      
      <div style="border-top: 1px solid #eee; padding-top: 20px; margin-top: 30px;">
        <p style="color: #888; font-size: 12px; text-align: center;">
          Dacă ai întrebări, nu ezita să ne <NAME_EMAIL>
        </p>
        <p style="color: #888; font-size: 12px; text-align: center;">
          © ${new Date().getFullYear()} Portavio. Toate drepturile rezervate.
        </p>
      </div>
    </div>
  `;

  const welcomeText = `
    Bun venit la Portavio!

    Salut${userName ? `, ${userName}` : ""}!

    Îți mulțumim că te-ai alăturat platformei Portavio! Email-ul tău a fost verificat cu succes și contul tău este acum activ.

    Acum poți începe să îți urmărești portofoliul în detaliu și să beneficiezi de toate funcționalitățile platformei noastre.

    Accesează profilul tău: ${
      process.env.BETTER_AUTH_URL || "http://localhost:3000"
    }/profile

    Dacă ai întrebări, nu ezita să ne <NAME_EMAIL>

    © ${new Date().getFullYear()} Portavio. Toate drepturile rezervate.
  `;

  await sendEmail({
    to: userEmail,
    subject: welcomeSubject,
    html: welcomeHtml,
    text: welcomeText,
  });
}

/**
 * Verify email token and handle welcome email sending
 * @param token JWT verification token
 * @returns VerificationResult with success status and details
 */
export async function verifyEmailToken(
  token: string
): Promise<VerificationResult> {
  let userEmail = "";

  // First, decode the token to get the user email
  try {
    const tokenPayload = JSON.parse(atob(token.split(".")[1]));
    userEmail = tokenPayload.email || "";
  } catch (decodeError) {
    console.error("Failed to decode token:", decodeError);
  }

  // Check if email was already verified BEFORE calling the verification API
  let wasAlreadyVerified = false;
  if (userEmail) {
    try {
      wasAlreadyVerified = await checkEmailVerificationStatus(userEmail);
    } catch (error) {
      console.error("Error checking verification status:", error);
    }
  }

  try {
    // Verify email using better-auth API
    const result: any = await auth.api.verifyEmail({
      query: { token },
    });

    // Check if verification was successful
    // The API returns { status: true } for success, not a user object
    if (!result || !result.status) {
      return {
        success: false,
        message: "Token-ul de verificare este invalid sau a expirat.",
        error: "INVALID_TOKEN",
      };
    }

    // If we couldn't decode the token earlier, handle it here
    if (!userEmail) {
      return {
        success: true,
        message: "Email-ul a fost verificat cu succes! Bun venit la Portavio!",
        welcomeEmailSent: false,
        error: "TOKEN_DECODE_FAILED",
      };
    }

    // Determine if this is a new verification or already verified
    const isNewVerification = !wasAlreadyVerified;

    if (isNewVerification) {
      // This is a new verification, send welcome email
      try {
        await sendWelcomeEmail(userEmail);

        return {
          success: true,
          message:
            "Email-ul a fost verificat cu succes! Bun venit la Portavio!",
          userEmail: userEmail,
          welcomeEmailSent: true,
          alreadyVerified: false,
          isNewVerification: true,
        };
      } catch (emailError) {
        console.error("Failed to send welcome email:", emailError);

        // Verification succeeded but welcome email failed
        return {
          success: true,
          message:
            "Email-ul a fost verificat cu succes! Bun venit la Portavio!",
          userEmail: userEmail,
          welcomeEmailSent: false,
          alreadyVerified: false,
          isNewVerification: true,
          error: "WELCOME_EMAIL_FAILED",
        };
      }
    } else {
      // Email was already verified, don't send welcome email
      return {
        success: true,
        message: "Email-ul este deja verificat. Bun venit înapoi la Portavio!",
        userEmail: userEmail,
        welcomeEmailSent: false,
        alreadyVerified: true,
        isNewVerification: false,
      };
    }
  } catch (error) {
    console.error("Email verification error:", error);

    if (error instanceof APIError) {
      // Handle specific better-auth errors
      switch (error.status) {
        case 400:
          return {
            success: false,
            message: "Token-ul de verificare este invalid.",
            error: "INVALID_TOKEN",
          };
        case 404:
          return {
            success: false,
            message: "Token-ul de verificare nu a fost găsit sau a expirat.",
            error: "TOKEN_NOT_FOUND",
          };
        case 410:
          return {
            success: false,
            message:
              "Token-ul de verificare a expirat. Vă rugăm să solicitați un nou email de verificare.",
            error: "TOKEN_EXPIRED",
          };
        default:
          return {
            success: false,
            message:
              "S-a produs o eroare la verificarea email-ului. Vă rugăm să încercați din nou.",
            error: "VERIFICATION_ERROR",
          };
      }
    }

    return {
      success: false,
      message:
        "S-a produs o eroare neașteptată. Vă rugăm să contactați suportul tehnic.",
      error: "UNEXPECTED_ERROR",
    };
  }
}
