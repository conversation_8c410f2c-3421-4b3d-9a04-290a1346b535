import { NextRequest, NextResponse } from "next/server";
import { sendTestEmail } from "@/lib/sendEmail";
import { z } from "zod";

// Validation schema for test email request
const testEmailSchema = z.object({
  testEmail: z
    .string({ required_error: "Adresa de email este obligatorie" })
    .email("Vă rugăm să introduceți o adresă de email validă")
    .max(255, "Adresa de email este prea lungă")
    .trim()
    .toLowerCase(),
});

export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();

    // Validate input
    const validation = testEmailSchema.safeParse(body);
    if (!validation.success) {
      const errors = validation.error.errors.map((err) => err.message);
      return NextResponse.json(
        {
          error: "Date invalide",
          details: errors,
        },
        { status: 400 }
      );
    }

    const { testEmail } = validation.data;

    // Send test email
    await sendTestEmail(testEmail);

    return NextResponse.json({
      success: true,
      message: `Email de test trimis cu succes la ${testEmail}`,
      sentTo: testEmail,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error("Test email API error:", error);

    // Handle different types of errors
    let errorMessage = "Eroare la trimiterea email-ului de test";
    let statusCode = 500;

    if (error instanceof Error) {
      errorMessage = error.message;
      
      // Check for specific error types
      if (error.message.includes("configurație")) {
        statusCode = 503; // Service Unavailable
      } else if (error.message.includes("SMTP") || error.message.includes("connection")) {
        statusCode = 502; // Bad Gateway
        errorMessage = "Eroare de conexiune SMTP. Verificați configurația serverului de email.";
      }
    }

    return NextResponse.json(
      {
        error: errorMessage,
        timestamp: new Date().toISOString(),
      },
      { status: statusCode }
    );
  }
}
