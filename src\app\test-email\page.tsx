"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "sonner";
import { Mail, Send, CheckCircle, AlertCircle } from "lucide-react";

export default function TestEmailPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [testEmail, setTestEmail] = useState("<EMAIL>");
  const [lastSentTo, setLastSentTo] = useState<string | null>(null);

  const handleSendTestEmail = async () => {
    if (!testEmail.trim()) {
      toast.error("Vă rugăm să introduceți o adresă de email");
      return;
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(testEmail)) {
      toast.error("Vă rugăm să introduceți o adresă de email validă");
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch("/api/test-email", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          testEmail: testEmail.trim(),
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Eroare la trimiterea email-ului");
      }

      toast.success("Email de test trimis cu succes!");
      setLastSentTo(testEmail.trim());
    } catch (error) {
      console.error("Test email error:", error);
      const errorMessage = error instanceof Error ? error.message : "Eroare necunoscută";
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-8 px-4 max-w-2xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2 flex items-center gap-2">
          <Mail className="h-8 w-8 text-portavio-orange" />
          Test Email SMTP
        </h1>
        <p className="text-muted-foreground">
          Testați configurația SMTP prin trimiterea unui email de test
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Send className="h-5 w-5" />
            Trimite Email de Test
          </CardTitle>
          <CardDescription>
            Introduceți adresa de email unde doriți să trimiteți email-ul de test
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="testEmail">Adresă de email</Label>
            <Input
              id="testEmail"
              type="email"
              placeholder="<EMAIL>"
              value={testEmail}
              onChange={(e) => setTestEmail(e.target.value)}
              disabled={isLoading}
            />
          </div>

          <Button
            onClick={handleSendTestEmail}
            disabled={isLoading || !testEmail.trim()}
            className="w-full"
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                Se trimite...
              </>
            ) : (
              <>
                <Send className="h-4 w-4 mr-2" />
                Trimite Email de Test
              </>
            )}
          </Button>

          {lastSentTo && (
            <div className="mt-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md">
              <div className="flex items-center gap-2 text-green-800 dark:text-green-200">
                <CheckCircle className="h-4 w-4" />
                <span className="text-sm font-medium">
                  Email de test trimis cu succes la: {lastSentTo}
                </span>
              </div>
            </div>
          )}

          <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md">
            <div className="flex items-start gap-2">
              <AlertCircle className="h-4 w-4 text-blue-600 dark:text-blue-400 mt-0.5" />
              <div className="text-sm text-blue-800 dark:text-blue-200">
                <p className="font-medium mb-1">Informații despre test:</p>
                <ul className="list-disc list-inside space-y-1 text-xs">
                  <li>Email-ul va fi trimis folosind configurația SMTP din baza de date</li>
                  <li>Verificați folderul spam dacă nu primiți email-ul</li>
                  <li>Email-ul conține un mesaj de test simplu în română</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
