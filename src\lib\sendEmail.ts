import nodemailer from "nodemailer";
import { hasuraQuery } from "@/utils/db";

interface MailConfig {
  smtp_host: string;
  smtp_port: number;
  smtp_user: string;
  smtp_pass: string;
  smtp_secure: boolean;
}

interface EmailPayload {
  to: string;
  subject: string;
  text?: string;
  html?: string;
  from?: string; // Optional custom from address
}

/**
 * Send email using configured SMTP settings from database
 * @param payload Email payload with recipient, subject, and content
 * @throws Error if no mail configuration found or sending fails
 */
export async function sendEmail(payload: EmailPayload): Promise<void> {
  try {
    const config = await getMailConfig();

    const transporter = nodemailer.createTransport({
      host: config.smtp_host,
      port: config.smtp_port,
      secure: config.smtp_secure,
      auth: {
        user: config.smtp_user,
        pass: config.smtp_pass,
      },
      tls: {
        rejectUnauthorized: false,
      },
    });

    await transporter.verify();

    const mailOptions = {
      from: payload.from || `"Portavio" <${config.smtp_user}>`,
      to: payload.to,
      subject: payload.subject,
      text: payload.text,
      html: payload.html,
    };

    const info = await transporter.sendMail(mailOptions);

    console.log("Email sent successfully:", {
      messageId: info.messageId,
      to: payload.to,
      subject: payload.subject,
    });
  } catch (error) {
    console.error("Failed to send email:", error);
    throw new Error(
      `Eroare la trimiterea email-ului: ${
        error instanceof Error ? error.message : "Eroare necunoscută"
      }`
    );
  }
}

/**
 * Get mail configuration from database
 * Fetches the default mail configuration or the first available one
 */
async function getMailConfig(): Promise<MailConfig> {
  try {
    const query = `
      query GetMailConfig {
        ptvuser_mail_config(
          where: { is_default: { _eq: true } }
          limit: 1
        ) {
          smtp_host
          smtp_port
          smtp_user
          smtp_pass
          smtp_secure
        }
      }
    `;

    const result = await hasuraQuery<{
      ptvuser_mail_config: MailConfig[];
    }>(query);

    let config = result.ptvuser_mail_config?.[0];

    // If no default config found, get the first available one
    if (!config) {
      const fallbackQuery = `
        query GetFirstMailConfig {
          ptvuser_mail_config(limit: 1) {
            smtp_host
            smtp_port
            smtp_user
            smtp_pass
            smtp_secure
          }
        }
      `;

      const fallbackResult = await hasuraQuery<{
        ptvuser_mail_config: MailConfig[];
      }>(fallbackQuery);

      config = fallbackResult.ptvuser_mail_config?.[0];
    }

    if (!config) {
      throw new Error(
        "Nu s-a găsit nicio configurație de email în baza de date"
      );
    }

    if (!config.smtp_host || !config.smtp_user || !config.smtp_pass) {
      throw new Error("Configurația de email este incompletă");
    }

    return config;
  } catch (error) {
    console.error("Failed to get mail configuration:", error);
    throw new Error(
      `Eroare la obținerea configurației de email: ${
        error instanceof Error ? error.message : "Eroare necunoscută"
      }`
    );
  }
}

/**
 * Send a test email to verify configuration
 * @param testEmail Email address to send test email to
 */
export async function sendTestEmail(testEmail: string): Promise<void> {
  const testPayload: EmailPayload = {
    to: testEmail,
    subject: "Test Email - Portavio",
    text: "Acesta este un email de test de la Portavio. Dacă primiți acest mesaj, configurația SMTP funcționează corect!",
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #ff7f50;">Test Email - Portavio</h2>
        <p>Acesta este un email de test de la <strong>Portavio</strong>.</p>
        <p>Dacă primiți acest mesaj, configurația SMTP funcționează corect!</p>
        <hr style="border: 1px solid #eee; margin: 20px 0;">
        <p style="color: #666; font-size: 12px;">
          Trimis la: ${new Date().toLocaleString("ro-RO")}
        </p>
      </div>
    `,
  };

  await sendEmail(testPayload);
}
