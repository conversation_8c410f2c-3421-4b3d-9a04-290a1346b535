import { betterAuth } from "better-auth";
import { nextCookies } from "better-auth/next-js";
import { username } from "better-auth/plugins";
import { Pool } from "pg";
import { sendEmail } from "./sendEmail";

export const auth = betterAuth({
  database: new Pool({
    database: process.env.DATABASE_NAME!,
    host: process.env.DATABASE_HOST!,
    password: process.env.DATABASE_PASSWORD!,
    port: Number(process.env.DATABASE_PORT!),
    user: process.env.DATABASE_USER!,
  }),
  secret: process.env.BETTER_AUTH_SECRET!,
  baseURL: process.env.BETTER_AUTH_URL!,
  trustedOrigins: ["http://localhost:3000", "https://portavio-dev.testbox.ro"],
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: true,
  },
  emailVerification: {
    sendVerificationEmail: async ({ user, url, token }) => {
      console.info("sendVerificationEmail:", { user, url, token });

      // Create custom verification URL pointing to our custom page
      const baseURL = process.env.BETTER_AUTH_URL || "http://localhost:3000";
      const verificationUrl = `${baseURL}/verify-email?token=${token}`;

      const emailSubject = "Verifică adresa de email - Portavio";
      const emailHtml = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #ff7f50; margin-bottom: 10px;">Verifică adresa de email</h1>
            <p style="color: #666; font-size: 16px;">Pentru a activa contul Portavio</p>
          </div>

          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
            <h2 style="color: #333; margin-bottom: 15px;">Salut${
              user.name ? `, ${user.name}` : ""
            }!</h2>
            <p style="color: #555; line-height: 1.6; margin-bottom: 15px;">
              Îți mulțumim că te-ai înregistrat pe Portavio! Pentru a-ți activa contul,
              te rugăm să verifici adresa de email făcând clic pe butonul de mai jos.
            </p>
            <p style="color: #555; line-height: 1.6;">
              Acest link va expira în 24 de ore din motive de securitate.
            </p>
          </div>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${verificationUrl}"
               style="background-color: #ff7f50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">
              Verifică Email-ul
            </a>
          </div>

          <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 6px; margin: 20px 0;">
            <p style="color: #856404; font-size: 14px; margin: 0;">
              <strong>Dacă nu te-ai înregistrat pe Portavio,</strong> poți ignora acest email în siguranță.
            </p>
          </div>

          <div style="border-top: 1px solid #eee; padding-top: 20px; margin-top: 30px;">
            <p style="color: #888; font-size: 12px; text-align: center;">
              Dacă butonul nu funcționează, copiază și lipește acest link în browser:<br>
              <a href="${verificationUrl}" style="color: #ff7f50; word-break: break-all;">${verificationUrl}</a>
            </p>
            <p style="color: #888; font-size: 12px; text-align: center; margin-top: 15px;">
              © ${new Date().getFullYear()} Portavio. Toate drepturile rezervate.
            </p>
          </div>
        </div>
      `;

      const emailText = `
Verifică adresa de email - Portavio

Salut${user.name ? `, ${user.name}` : ""}!

Îți mulțumim că te-ai înregistrat pe Portavio! Pentru a-ți activa contul, te rugăm să verifici adresa de email accesând link-ul de mai jos:

${verificationUrl}

Acest link va expira în 24 de ore din motive de securitate.

Dacă nu te-ai înregistrat pe Portavio, poți ignora acest email în siguranță.

© ${new Date().getFullYear()} Portavio. Toate drepturile rezervate.
      `;

      await sendEmail({
        to: user.email,
        subject: emailSubject,
        html: emailHtml,
        text: emailText,
      });
    },
    sendOnSignUp: true,
  },
  socialProviders: {
    google: {
      prompt: "select_account",
      clientId: process.env.GOOGLE_CLIENT_ID as string,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
    },
  },
  plugins: [
    username({
      minUsernameLength: 3,
      maxUsernameLength: 30,
      usernameValidator: (username) => {
        // Only allow alphanumeric characters, underscores, and dots
        const validPattern = /^[a-zA-Z0-9_.]+$/;
        if (!validPattern.test(username)) {
          return false;
        }
        // Prevent reserved usernames
        const reservedUsernames = [
          "admin",
          "root",
          "system",
          "api",
          "www",
          "portavio",
        ];
        return !reservedUsernames.includes(username.toLowerCase());
      },
    }),
    nextCookies(),
  ],
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // 1 day
  },
  user: {
    additionalFields: {
      username: {
        type: "string",
        required: false,
        unique: true,
      },
      displayUsername: {
        type: "string",
        required: false,
      },
      agreedTerms: {
        type: "boolean",
        required: false,
        defaultValue: true,
        input: true,
      },
    },
  },
  account: {
    accountLinking: {
      enabled: false,
      allowDifferentEmails: false,
    },
  },
});

export type Session = typeof auth.$Infer.Session;
export type User = typeof auth.$Infer.Session.user;
