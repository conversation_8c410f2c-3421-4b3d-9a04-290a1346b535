import { z } from "zod";

// Base username validation schema
const usernameValidation = z
  .string()
  .min(3, "Username-ul trebuie să aibă cel puțin 3 caractere")
  .max(30, "Username-ul nu poate avea mai mult de 30 de caractere")
  .regex(
    /^[a-zA-Z0-9_.]+$/,
    "Username-ul poate conține doar litere, cifre, puncte și underscore"
  )
  .refine(
    (username) => {
      const reservedUsernames = [
        "admin",
        "root",
        "system",
        "api",
        "www",
        "portavio",
      ];
      return !reservedUsernames.includes(username.toLowerCase());
    },
    {
      message: "Acest username nu este disponibil",
    }
  );

// Account Details Schema (for form validation - all fields required)
export const accountDetailsSchema = z.object({
  email: z.string().min(1, "Email-ul este obligatoriu").email("Email invalid"),
  username: usernameValidation,
  name: z
    .string()
    .min(1, "Numele este obligatoriu")
    .max(100, "Numele nu poate avea mai mult de 100 de caractere"),
});

// Dynamic schema creator based on account capabilities
export function createAccountDetailsSchema(
  capabilities: AccountCapabilities,
  initialUsername?: string | null
) {
  const baseSchema = {
    email: z
      .string()
      .min(1, "Email-ul este obligatoriu")
      .email("Email invalid"),
    name: z
      .string()
      .min(1, "Numele este obligatoriu")
      .max(100, "Numele nu poate avea mai mult de 100 de caractere"),
  };

  // If user can change username, apply validation
  if (capabilities.canChangeUsername) {
    // For Google accounts with no initial username, make it optional but validate when provided
    if (capabilities.isGoogleAccount && !initialUsername) {
      return z.object({
        ...baseSchema,
        username: z
          .string()
          .optional()
          .refine(
            (username) => {
              // If username is provided, it must pass validation
              if (username && username.trim()) {
                return usernameValidation.safeParse(username).success;
              }
              return true; // Allow empty/undefined for Google accounts without initial username
            },
            {
              message:
                "Username-ul trebuie să aibă cel puțin 3 caractere și poate conține doar litere, cifre, puncte și underscore",
            }
          ),
      });
    }

    // For credential accounts or Google accounts with existing username, require validation
    return z.object({
      ...baseSchema,
      username: usernameValidation,
    });
  }

  // For accounts that cannot change username, make it optional
  return z.object({
    ...baseSchema,
    username: z.string().optional(),
  });
}

// Account Details Update Schema (for API - only changed fields)
export const accountDetailsUpdateSchema = z
  .object({
    email: z
      .string()
      .min(1, "Email-ul este obligatoriu")
      .email("Email invalid")
      .optional(),
    username: z
      .string()
      .optional()
      .refine(
        (username) => {
          // If username is provided and not empty, validate it
          if (username && username.trim()) {
            return usernameValidation.safeParse(username).success;
          }
          return true; // Allow empty/undefined usernames
        },
        {
          message:
            "Username-ul trebuie să aibă cel puțin 3 caractere și poate conține doar litere, cifre, puncte și underscore",
        }
      ),
    name: z
      .string()
      .min(1, "Numele este obligatoriu")
      .max(100, "Numele nu poate avea mai mult de 100 de caractere")
      .optional(),
  })
  .refine((data) => Object.keys(data).length > 0, {
    message: "Cel puțin un câmp trebuie să fie modificat",
  });

// Password Change Schema
export const passwordChangeSchema = z
  .object({
    currentPassword: z.string().min(1, "Parola curentă este obligatorie"),
    newPassword: z
      .string()
      .min(8, "Parola nouă trebuie să aibă cel puțin 8 caractere")
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
        "Parola trebuie să conțină cel puțin o literă mică, o literă mare și o cifră"
      ),
    confirmNewPassword: z.string(),
  })
  .refine((data) => data.newPassword === data.confirmNewPassword, {
    message: "Parolele nu se potrivesc",
    path: ["confirmNewPassword"],
  });

// Profile Picture Upload Schema
export const profilePictureSchema = z.object({
  file: z
    .instanceof(File)
    .refine(
      (file) => file.size <= 3 * 1024 * 1024,
      "Fișierul nu poate depăși 3MB"
    )
    .refine(
      (file) =>
        ["image/jpeg", "image/jpg", "image/png", "image/webp"].includes(
          file.type
        ),
      "Doar fișierele JPEG, PNG și WebP sunt acceptate"
    ),
});

// TypeScript types
export type AccountDetailsFormData = z.infer<typeof accountDetailsSchema>;
export type AccountDetailsFormDataFlexible = {
  email: string;
  username?: string;
  name: string;
};
export type AccountDetailsUpdateData = z.infer<
  typeof accountDetailsUpdateSchema
>;
export type PasswordChangeFormData = z.infer<typeof passwordChangeSchema>;
export type ProfilePictureFormData = z.infer<typeof profilePictureSchema>;

// User Account Data Types
export interface UserAccountData {
  id: string;
  email: string;
  username?: string;
  name: string;
  image?: string;
  createdAt: string;
  updatedAt: string;
  emailVerified: boolean;
  accounts: UserAccount[];
  profile_picture?: ProfilePicture;
}

export interface UserAccount {
  id: string;
  providerId: string;
  accountId: string;
  createdAt: string;
  updatedAt: string;
}

export interface ProfilePicture {
  user_id: string;
  image_data: string;
  image_type: string;
  file_size: number;
  created_at: string;
  updated_at: string;
}

export type AuthProvider = "credential" | "google";

export interface AccountCapabilities {
  canChangeEmail: boolean;
  canChangePassword: boolean;
  canChangeUsername: boolean;
  canChangeName: boolean;
  canUploadProfilePicture: boolean;
  isGoogleAccount: boolean;
}

export function getAccountCapabilities(
  accounts: UserAccount[]
): AccountCapabilities {
  const hasCredentialAccount = accounts.some(
    (account) => account.providerId === "credential"
  );
  const hasGoogleAccount = accounts.some(
    (account) => account.providerId === "google"
  );

  return {
    canChangeEmail: hasCredentialAccount,
    canChangePassword: hasCredentialAccount,
    canChangeUsername: hasCredentialAccount || hasGoogleAccount, // Allow both credential and Google accounts to change username
    canChangeName: true, // Name can always be changed
    canUploadProfilePicture: true, // Profile picture can always be uploaded
    isGoogleAccount: hasGoogleAccount && !hasCredentialAccount, // True only for pure Google accounts
  };
}

// Helper function to determine image source type
export function getImageSourceType(imageUrl?: string): "url" | "base64" | null {
  if (!imageUrl) return null;

  if (imageUrl.startsWith("data:image/")) {
    return "base64";
  }

  if (imageUrl.startsWith("http://") || imageUrl.startsWith("https://")) {
    return "url";
  }

  return null;
}

export function getChangedFields<T extends Record<string, any>>(
  newData: T,
  originalData: Partial<T>
): Partial<T> {
  const changedFields: Partial<T> = {};

  for (const [key, value] of Object.entries(newData)) {
    const originalValue = originalData[key];

    if (value !== originalValue) {
      if (typeof value === "string" && typeof originalValue === "string") {
        if (value.trim() !== originalValue.trim()) {
          (changedFields as any)[key] = value;
        }
      } else if (value !== originalValue) {
        (changedFields as any)[key] = value;
      }
    }
  }

  return changedFields;
}
